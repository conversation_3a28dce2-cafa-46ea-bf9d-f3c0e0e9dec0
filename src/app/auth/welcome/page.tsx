'use client';

import { useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { AuthLayout } from '@/shared/components/layout';
import { Button } from '@/shared/components/ui';
import { SUBSCRIPTION_PLANS, USER_TYPE_LABELS } from '@/features/auth';
import type { UserRole, PlanType } from '@/types/auth';

export default function WelcomePage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const userType = searchParams.get('type') || 'client';
  const plan = searchParams.get('plan') || 'annual';

  const getUserTypeLabel = (type: string) => {
    switch (type) {
      case 'barber':
        return 'Barber';
      case 'shop_owner':
        return 'Shop Owner';
      default:
        return 'Client';
    }
  };

  const getPlanLabel = (planType: string) => {
    switch (planType) {
      case 'annual':
        return 'Annual Plan ($95.99/year)';
      case 'monthly':
        return 'Monthly Plan ($12.99/month)';
      default:
        return 'Free Trial';
    }
  };

  const handleGetStarted = () => {
    // Navigate to appropriate dashboard based on user type
    switch (userType) {
      case 'barber':
        router.push('/dashboard/barber');
        break;
      case 'shop_owner':
        router.push('/dashboard/shop_owner');
        break;
      default:
        router.push('/dashboard');
        break;
    }
  };

  const heroContent = (
    <div className="text-center space-y-6">
      <div className="space-y-4">
        <div className="w-20 h-20 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto">
          <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
        </div>
        
        <div>
          <h2 className="text-white text-3xl lg:text-4xl font-bold mb-2">
            Welcome!
          </h2>
          <p className="text-white/80 text-lg">
            Your account has been created successfully
          </p>
        </div>
      </div>
    </div>
  );

  return (
    <AuthLayout heroContent={heroContent}>
      <div className="space-y-8 text-center">
        {/* Success Message */}
        <div className="space-y-4">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
            <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
          
          <div>
            <h3 className="text-2xl font-bold text-gray-900 mb-2">
              Registration Complete!
            </h3>
            <p className="text-gray-600">
              You're all set to start your barber booking journey
            </p>
          </div>
        </div>

        {/* Account Details */}
        <div className="bg-gray-50 rounded-xl p-6 space-y-4">
          <h4 className="font-semibold text-gray-900">Account Details</h4>
          
          <div className="space-y-3 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600">Account Type:</span>
              <span className="font-medium text-gray-900">{getUserTypeLabel(userType)}</span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-gray-600">Selected Plan:</span>
              <span className="font-medium text-gray-900">{getPlanLabel(plan)}</span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-gray-600">Trial Period:</span>
              <span className="font-medium text-green-600">7 days free</span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-gray-600">Status:</span>
              <span className="font-medium text-green-600">Active</span>
            </div>
          </div>
        </div>

        {/* Next Steps */}
        <div className="space-y-4">
          <h4 className="font-semibold text-gray-900">What's Next?</h4>
          
          <div className="space-y-3 text-left">
            {userType === 'client' && (
              <>
                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-amber-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-amber-600 text-sm font-medium">1</span>
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">Find Barbers</p>
                    <p className="text-sm text-gray-600">Browse and discover talented barbers in your area</p>
                  </div>
                </div>
                
                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-amber-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-amber-600 text-sm font-medium">2</span>
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">Book Appointments</p>
                    <p className="text-sm text-gray-600">Schedule your haircut at your convenience</p>
                  </div>
                </div>
                
                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-amber-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-amber-600 text-sm font-medium">3</span>
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">Enjoy Your Service</p>
                    <p className="text-sm text-gray-600">Get the perfect haircut and leave reviews</p>
                  </div>
                </div>
              </>
            )}
            
            {userType === 'barber' && (
              <>
                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-amber-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-amber-600 text-sm font-medium">1</span>
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">Complete Your Profile</p>
                    <p className="text-sm text-gray-600">Add your services, portfolio, and availability</p>
                  </div>
                </div>
                
                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-amber-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-amber-600 text-sm font-medium">2</span>
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">Set Your Schedule</p>
                    <p className="text-sm text-gray-600">Configure your working hours and availability</p>
                  </div>
                </div>
                
                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-amber-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-amber-600 text-sm font-medium">3</span>
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">Start Accepting Bookings</p>
                    <p className="text-sm text-gray-600">Begin receiving and managing client appointments</p>
                  </div>
                </div>
              </>
            )}
            
            {userType === 'shop_owner' && (
              <>
                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-amber-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-amber-600 text-sm font-medium">1</span>
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">Set Up Your Shop</p>
                    <p className="text-sm text-gray-600">Add shop details, services, and photos</p>
                  </div>
                </div>
                
                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-amber-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-amber-600 text-sm font-medium">2</span>
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">Invite Your Barbers</p>
                    <p className="text-sm text-gray-600">Add your team members and manage their profiles</p>
                  </div>
                </div>
                
                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-amber-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-amber-600 text-sm font-medium">3</span>
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">Manage Your Business</p>
                    <p className="text-sm text-gray-600">Track bookings, revenue, and analytics</p>
                  </div>
                </div>
              </>
            )}
          </div>
        </div>

        {/* Get Started Button */}
        <Button
          onClick={handleGetStarted}
          className="w-full"
          size="lg"
        >
          Get Started
        </Button>
      </div>
    </AuthLayout>
  );
}

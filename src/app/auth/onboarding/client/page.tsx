'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { AuthLayout } from '@/shared/components/layout';
import { ProgressIndicator, Button } from '@/shared/components/ui';
import { HairPreferencesStep, BudgetLanguageStep } from '@/features/onboarding';
import type { OnboardingData } from '@/types/onboarding';

export default function ClientOnboardingPage() {
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState(1);
  const [onboardingData, setOnboardingData] = useState<OnboardingData>({
    hairLength: [],
    hairType: [],
    preferredStyles: [],
    budget: [],
    language: [],
  });

  const totalSteps = 2;

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    } else {
      router.push('/auth/verify-email');
    }
  };

  const handleNext = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    } else {
      handleComplete();
    }
  };

  const handleSkip = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    } else {
      handleComplete();
    }
  };

  const handleComplete = () => {
    // Mock API call to save onboarding data
    console.log('Onboarding completed:', onboardingData);
    // Redirect to welcome screen after preferences are set
    router.push('/auth/welcome');
  };

  const updateOnboardingData = (stepData: Partial<OnboardingData>) => {
    setOnboardingData(prev => ({ ...prev, ...stepData }));
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <HairPreferencesStep
            data={onboardingData}
            onUpdate={updateOnboardingData}
          />
        );
      case 2:
        return (
          <BudgetLanguageStep
            data={onboardingData}
            onUpdate={updateOnboardingData}
          />
        );
      default:
        return null;
    }
  };

  return (
    <AuthLayout 
      title="Register" 
      showBackButton={true} 
      onBack={handleBack}
    >
      <div className="space-y-6">
        {/* Progress Indicator */}
        <ProgressIndicator
          currentStep={currentStep}
          totalSteps={totalSteps}
          className="mb-8"
        />

        {/* Step Content */}
        <div className="min-h-[400px]">
          {renderStep()}
        </div>

        {/* Navigation Buttons */}
        <div className="flex gap-3 pt-6">
          <Button
            variant="outline"
            onClick={handleSkip}
            className="flex-1"
            size="lg"
          >
            Skip
          </Button>
          <Button
            onClick={handleNext}
            className="flex-1"
            size="lg"
          >
            {currentStep === totalSteps ? 'Save & Continue' : 'Next'}
          </Button>
        </div>
      </div>
    </AuthLayout>
  );
}

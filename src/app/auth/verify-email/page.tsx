'use client';

import { useRouter, useSearchParams } from 'next/navigation';
import { AuthLayout } from '@/shared/components/layout';
import { VerifyEmail } from '@/features/auth';

export default function VerifyEmailPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const userType = searchParams.get('type') || 'client';

  const handleBack = () => {
    router.push('/auth/signup');
  };

  const handleVerificationSuccess = () => {
    // For clients, go to preferences/onboarding, then welcome screen
    if (userType === 'client') {
      router.push('/auth/onboarding/client');
    } else if (userType === 'barber' || userType === 'shop_owner') {
      // Barbers and shop owners go to plan selection
      router.push(`/auth/select-plan?type=${userType}`);
    } else {
      // Default to welcome screen
      router.push('/auth/welcome');
    }
  };

  return (
    <AuthLayout
      title="Verify Email"
      showBackButton={true}
      onBack={handleBack}
      contentType="simple"
    >
      <VerifyEmail onSuccess={handleVerificationSuccess} />
    </AuthLayout>
  );
}

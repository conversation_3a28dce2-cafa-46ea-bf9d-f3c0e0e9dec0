'use client';

import { useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { AuthLayout } from '@/shared/components/layout';
import { Button } from '@/shared/components/ui';
import { SIGNUP_TABS, SUBSCRIPTION_PLANS } from '@/features/auth';
import type { UserRole, PlanType } from '@/types/auth';

const plans = SUBSCRIPTION_PLANS;

export default function SelectPlanPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const userType = searchParams.get('type') || 'client';
  const [selectedPlan, setSelectedPlan] = useState<PlanType>('annual');

  const handleBack = () => {
    router.back();
  };

  const handleStartTrial = () => {
    // For now, just navigate to a welcome screen
    // Later this will integrate with Stripe
    console.log('Starting trial with plan:', selectedPlan, 'for user type:', userType);
    router.push(`/auth/welcome?type=${userType}&plan=${selectedPlan}`);
  };

  const tabs = SIGNUP_TABS;

  const heroContent = (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-white text-3xl lg:text-4xl font-bold mb-2">Register</h2>
        <p className="text-white/80 text-lg">to start you free trial</p>
      </div>
      
      {/* Tab Selector - Read Only */}
      <div className="flex bg-white/10 backdrop-blur-md rounded-2xl p-2 gap-2 border border-white/20">
        {tabs.map((tab) => (
          <div
            key={tab.id}
            className={`flex-1 flex flex-col items-center justify-center py-4 px-3 rounded-xl min-h-[80px] border ${
              tab.id === userType
                ? 'bg-white/20 text-white shadow-lg backdrop-blur-sm border-white/30'
                : 'text-white/40 border-transparent'
            }`}
          >
            <div className="mb-2 opacity-90">
              {tab.icon}
            </div>
            <span className="text-xs lg:text-sm">{tab.label}</span>
          </div>
        ))}
      </div>
    </div>
  );

  return (
    <AuthLayout
      showBackButton={true}
      onBack={handleBack}
      heroContent={heroContent}
      contentType="complex"
    >
      <div className="space-y-6">
        {/* Plan Selection */}
        <div className="space-y-4">
          {plans.map((plan) => (
            <div
              key={plan.id}
              onClick={() => setSelectedPlan(plan.id)}
              className={`relative p-6 rounded-2xl border-2 cursor-pointer transition-all duration-200 ${
                selectedPlan === plan.id
                  ? 'border-green-500 bg-green-50'
                  : 'border-gray-200 bg-white hover:border-gray-300'
              }`}
            >
              {plan.badge && (
                <div className="absolute -top-3 left-6">
                  <span className="bg-green-500 text-white text-sm font-medium px-3 py-1 rounded-full">
                    {plan.badge}
                  </span>
                </div>
              )}
              
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <h3 className="text-xl font-bold text-gray-900 mb-1">
                    {plan.name}
                  </h3>
                  <p className="text-gray-600 text-sm">
                    {plan.description}
                  </p>
                </div>
                
                <div className="flex items-center gap-3">
                  <div className="text-right">
                    <div className="text-2xl font-bold text-gray-900">
                      {plan.price}
                    </div>
                    <div className="text-sm text-gray-500">
                      / {plan.period}
                    </div>
                  </div>
                  
                  <div className={`w-6 h-6 rounded-full border-2 flex items-center justify-center ${
                    selectedPlan === plan.id
                      ? 'border-green-500 bg-green-500'
                      : 'border-gray-300'
                  }`}>
                    {selectedPlan === plan.id && (
                      <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Start Trial Button */}
        <Button
          onClick={handleStartTrial}
          className="w-full bg-gray-900 hover:bg-gray-800 text-white"
          size="lg"
        >
          Start 1 - week free trial
        </Button>

        {/* Back Button */}
        <div className="text-center">
          <button
            onClick={handleBack}
            className="text-amber-600 hover:text-amber-700 font-medium"
          >
            Back
          </button>
        </div>
      </div>
    </AuthLayout>
  );
}

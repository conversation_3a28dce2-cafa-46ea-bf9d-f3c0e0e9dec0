'use client';

import { useState } from 'react';
import { Button, SuccessModal, OTPInput } from '@/shared/components/ui';

interface VerifyEmailProps {
  onSuccess: () => void;
}

export function VerifyEmail({ onSuccess }: VerifyEmailProps) {
  const [code, setCode] = useState('');
  const [showSuccessModal, setShowSuccessModal] = useState(false);

  const handleOTPComplete = (otpValue: string) => {
    setCode(otpValue);
  };

  const handleVerify = () => {
    if (code.length === 6) {
      // Mock verification - in real app, this would call API
      console.log('Verifying code:', code);
      setShowSuccessModal(true);
    }
  };

  const handleResend = () => {
    // Mock resend - in real app, this would call API
    console.log('Resending verification code');
    setCode('');
  };

  const handleSuccessModalClose = () => {
    setShowSuccessModal(false);
    onSuccess();
  };

  const isCodeComplete = code.length === 6;

  return (
    <>
      <div className="space-y-6">
        <div className="text-center">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            Enter Verification Code
          </h3>
        </div>

        {/* OTP Input */}
        <div className="flex justify-center">
          <OTPInput
            length={6}
            onComplete={handleOTPComplete}
            onChange={setCode}
            className="gap-3"
          />
        </div>

        {/* Verify Button */}
        <Button
          onClick={handleVerify}
          disabled={!isCodeComplete}
          className="w-full"
          size="lg"
        >
          Verify
        </Button>

        {/* Resend Link */}
        <div className="text-center">
          <span className="text-gray-600">Haven't received OTP? </span>
          <button
            onClick={handleResend}
            className="text-amber-800 hover:text-amber-900 font-medium"
          >
            Resend
          </button>
        </div>
      </div>

      {/* Success Modal */}
      <SuccessModal
        isOpen={showSuccessModal}
        onClose={handleSuccessModalClose}
        title="Email Verified!"
        buttonText="Continue"
        onButtonClick={handleSuccessModalClose}
      />
    </>
  );
}

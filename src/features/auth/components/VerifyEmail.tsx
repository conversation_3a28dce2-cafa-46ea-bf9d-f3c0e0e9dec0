'use client';

import { useState } from 'react';
import { useSearchParams } from 'next/navigation';
import { Button, SuccessModal, OTPInput } from '@/shared/components/ui';
import { authApiService } from '../services/auth-api';

interface VerifyEmailProps {
  onSuccess: () => void;
}

export function VerifyEmail({ onSuccess }: VerifyEmailProps) {
  const [code, setCode] = useState('');
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const searchParams = useSearchParams();

  // Get email from URL params or localStorage (from signup)
  const getEmail = () => {
    const emailParam = searchParams.get('email');
    if (emailParam) return emailParam;

    // Try to get from stored user data
    const currentUser = authApiService.getCurrentUser();
    return currentUser?.email || '';
  };

  const handleOTPComplete = (otpValue: string) => {
    setCode(otpValue);
    setError(null);
  };

  const handleVerify = async () => {
    if (code.length === 6) {
      const email = getEmail();
      if (!email) {
        setError('Email not found. Please try signing up again.');
        return;
      }

      try {
        setIsLoading(true);
        setError(null);

        // Call the verify email OTP API
        await authApiService.verifyEmailOTP(email, code);
        setShowSuccessModal(true);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Verification failed');
      } finally {
        setIsLoading(false);
      }
    }
  };

  const handleResend = async () => {
    const email = getEmail();
    if (!email) {
      setError('Email not found. Please try signing up again.');
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      // Resend OTP
      await authApiService.sendOTP(email, 'email-verification');
      setCode('');
      // Could show a success message here
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to resend OTP');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSuccessModalClose = () => {
    setShowSuccessModal(false);
    onSuccess();
  };

  const isCodeComplete = code.length === 6;
  const email = getEmail();

  return (
    <>
      <div className="space-y-6">
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md text-sm">
            {error}
          </div>
        )}

        <div className="text-center">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            Enter Verification Code
          </h3>
          {email && (
            <p className="text-sm text-gray-600">
              We've sent a 6-digit code to <span className="font-medium">{email}</span>
            </p>
          )}
        </div>

        {/* OTP Input */}
        <div className="flex justify-center">
          <OTPInput
            length={6}
            onComplete={handleOTPComplete}
            onChange={setCode}
            className="gap-3"
          />
        </div>

        {/* Verify Button */}
        <Button
          onClick={handleVerify}
          disabled={!isCodeComplete || isLoading}
          className="w-full"
          size="lg"
        >
          {isLoading ? 'Verifying...' : 'Verify'}
        </Button>

        {/* Resend Link */}
        <div className="text-center">
          <span className="text-gray-600">Haven't received OTP? </span>
          <button
            onClick={handleResend}
            disabled={isLoading}
            className="text-amber-800 hover:text-amber-900 font-medium disabled:opacity-50"
          >
            {isLoading ? 'Sending...' : 'Resend'}
          </button>
        </div>
      </div>

      {/* Success Modal */}
      <SuccessModal
        isOpen={showSuccessModal}
        onClose={handleSuccessModalClose}
        title="Email Verified!"
        buttonText="Continue"
        onButtonClick={handleSuccessModalClose}
      />
    </>
  );
}

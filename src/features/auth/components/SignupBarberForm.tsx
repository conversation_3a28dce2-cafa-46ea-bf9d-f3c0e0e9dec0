'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button, Input, Select } from '@/shared/components/ui';
import { useFormValidation, createEmailValidation, createPasswordValidation, createRequiredValidation, createPhoneValidation } from '@/shared/hooks';
import { useAuth } from '../hooks/use-auth';
import Image from 'next/image';

interface BarberFormData {
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  password: string;
  // Optional fields for all roles
  profilePhoto?: string;
  // Barber-specific fields (when role is "barber")
  bio?: string; // Optional in backend
  experienceYears: string; // Maps to experience field: "0-1", "1-3", "3-5", "5-10", "10-15", "15-20", "20+"
  specialties?: string[]; // Optional: ["haircut", "beard", "styling"]
  vatNumber: string; // 8-50 characters
  workingType?: string; // Optional: "solo" or "in_shop"
  portfolioUrl?: string; // Optional
  isKidFriendly?: boolean; // Optional
  isAvailableForLastMinuteOffer?: boolean; // Optional
  // Frontend-only fields (not in API schema) - commented out
  // language: string; // Commented out - not in API schema
  // address: Address; // Commented out - not in API schema
  // barberCertificate: File | null; // Commented out - not in API schema
}

const experienceOptions = [
  { value: '0-1', label: '0-1 years' },
  { value: '1-3', label: '1-3 years' },
  { value: '3-5', label: '3-5 years' },
  { value: '5-10', label: '5-10 years' },
  { value: '10-15', label: '10-15 years' },
  { value: '15-20', label: '15-20 years' },
  { value: '20+', label: '20+ years' },
];

const specialtyOptions = [
  { value: 'haircut', label: 'Haircut' },
  { value: 'beard', label: 'Beard Styling' },
  { value: 'styling', label: 'Hair Styling' },
  { value: 'coloring', label: 'Hair Coloring' },
  { value: 'shaving', label: 'Traditional Shaving' },
];

const workingTypeOptions = [
  { value: 'solo', label: 'Solo Practice' },
  { value: 'in_shop', label: 'In Shop' },
];

const languageOptions = [
  { value: 'en', label: 'English' },
  { value: 'es', label: 'Spanish' },
  { value: 'fr', label: 'French' },
  { value: 'de', label: 'German' },
  { value: 'it', label: 'Italian' },
  { value: 'pt', label: 'Portuguese' },
  { value: 'nl', label: 'Dutch' },
  { value: 'ar', label: 'Arabic' },
];

export function SignupBarberForm() {
  const router = useRouter();
  const [showPassword, setShowPassword] = useState(false);
  const { register } = useAuth();

  // Use standardized form validation
  const { formState, setValue, handleSubmit, setTouched, validateField } = useFormValidation<BarberFormData>({
    initialData: {
      firstName: '',
      lastName: '',
      email: '',
      phoneNumber: '',
      password: '',
      // Optional fields for all roles
      profilePhoto: '',
      // Barber-specific fields
      bio: '',
      experienceYears: '',
      specialties: [],
      vatNumber: '',
      workingType: 'solo',
      portfolioUrl: '',
      isKidFriendly: false,
      isAvailableForLastMinuteOffer: false,
    },
    validationRules: {
      firstName: createRequiredValidation('First name'),
      lastName: createRequiredValidation('Last name'),
      email: createEmailValidation(),
      phoneNumber: createPhoneValidation(),
      password: createPasswordValidation(),
      experienceYears: createRequiredValidation('Experience years'),
      vatNumber: createRequiredValidation('VAT number'),
      // Optional fields don't need validation rules
    },
    onSubmit: async (data) => {
      try {
        // Call the register function with barber role
        await register(data, 'barber');
        // The register function will handle the redirect
      } catch (error) {
        console.error('Barber registration failed:', error);
        // Error handling is managed by the useAuth hook
      }
    },
  });

  const handleSpecialtiesChange = (specialty: string) => {
    const currentSpecialties = formState.data.specialties || [];
    const newSpecialties = currentSpecialties.includes(specialty)
      ? currentSpecialties.filter(s => s !== specialty)
      : [...currentSpecialties, specialty];
    setValue('specialties', newSpecialties);
  };

  const handleGoogleSignUp = () => {
    console.log('Google sign-up clicked');
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4 lg:space-y-5">
      {/* Name Fields */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-3 lg:gap-4">
        <Input
          type="text"
          name="firstName"
          placeholder="Enter your First Name"
          value={formState.data.firstName}
          onChange={(e) => setValue('firstName', e.target.value)}
          onBlur={() => {
            setTouched('firstName', true);
            // Use setTimeout to ensure setValue has processed before validation
            setTimeout(() => validateField('firstName'), 0);
          }}
          error={formState.errors.firstName}
          required
          icon={
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
          }
          label="First Name"
        />

        <Input
          type="text"
          name="lastName"
          placeholder="Enter your Last Name"
          value={formState.data.lastName}
          onChange={(e) => setValue('lastName', e.target.value)}
          onBlur={() => {
            setTouched('lastName', true);
            setTimeout(() => validateField('lastName'), 0);
          }}
          error={formState.errors.lastName}
          required
          icon={
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
          }
          label="Last Name"
        />
      </div>

      {/* Email */}
      <Input
        type="email"
        name="email"
        placeholder="Enter your Email"
        value={formState.data.email}
        onChange={(e) => setValue('email', e.target.value)}
        onBlur={() => {
          setTouched('email', true);
          setTimeout(() => validateField('email'), 0);
        }}
        error={formState.errors.email}
        required
        icon={
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207" />
          </svg>
        }
        label="Email"
      />

      {/* Phone Number */}
      <Input
        type="tel"
        name="phoneNumber"
        placeholder="Enter your Phone Number"
        value={formState.data.phoneNumber}
        onChange={(e) => setValue('phoneNumber', e.target.value)}
        onBlur={() => {
          setTouched('phoneNumber', true);
          setTimeout(() => validateField('phoneNumber'), 0);
        }}
        error={formState.errors.phoneNumber}
        required
        icon={
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
          </svg>
        }
        label="Phone Number"
      />

      {/* Bio */}
      <Input
        type="text"
        name="bio"
        placeholder="Tell us about yourself (optional)"
        value={formState.data.bio}
        onChange={(e) => setValue('bio', e.target.value)}
        icon={
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
          </svg>
        }
        label="Bio"
      />

      {/* Experience Years */}
      <Select
        options={experienceOptions}
        value={formState.data.experienceYears}
        onChange={(value) => {
          setValue('experienceYears', value);
          setTouched('experienceYears', true);
          setTimeout(() => validateField('experienceYears'), 0);
        }}
        placeholder="Select your experience level"
        label="Experience Years"
        error={formState.errors.experienceYears}
        required
        icon={
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0H8m8 0v2a2 2 0 01-2 2H10a2 2 0 01-2-2V6m8 0H8" />
          </svg>
        }
      />

      {/* Specialties */}
      <div className="space-y-3">
        <label className="block text-sm font-medium text-gray-700">
          Specialties (optional)
        </label>
        <div className="grid grid-cols-2 gap-2">
          {specialtyOptions.map((specialty) => (
            <label key={specialty.value} className="flex items-center space-x-2 cursor-pointer">
              <input
                type="checkbox"
                checked={formState.data.specialties?.includes(specialty.value) || false}
                onChange={() => handleSpecialtiesChange(specialty.value)}
                className="rounded border-gray-300 text-amber-600 focus:ring-amber-500"
              />
              <span className="text-sm text-gray-700">{specialty.label}</span>
            </label>
          ))}
        </div>
      </div>

      {/* Working Type */}
      <Select
        options={workingTypeOptions}
        value={formState.data.workingType}
        onChange={(value) => setValue('workingType', value)}
        placeholder="Select your working type"
        label="Working Type (optional)"
        icon={
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
          </svg>
        }
      />

      {/* Portfolio URL */}
      <Input
        type="url"
        name="portfolioUrl"
        placeholder="Enter your portfolio URL (optional)"
        value={formState.data.portfolioUrl}
        onChange={(e) => setValue('portfolioUrl', e.target.value)}
        icon={
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
          </svg>
        }
        label="Portfolio URL"
      />

      {/* Additional Options */}
      <div className="space-y-3">
        <label className="block text-sm font-medium text-gray-700">
          Additional Options
        </label>
        <div className="space-y-2">
          <label className="flex items-center space-x-2 cursor-pointer">
            <input
              type="checkbox"
              name="isKidFriendly"
              checked={formState.data.isKidFriendly}
              onChange={(e) => setValue('isKidFriendly', e.target.checked)}
              className="rounded border-gray-300 text-amber-600 focus:ring-amber-500"
            />
            <span className="text-sm text-gray-700">Kid Friendly</span>
          </label>
          <label className="flex items-center space-x-2 cursor-pointer">
            <input
              type="checkbox"
              name="isAvailableForLastMinuteOffer"
              checked={formState.data.isAvailableForLastMinuteOffer}
              onChange={(e) => setValue('isAvailableForLastMinuteOffer', e.target.checked)}
              className="rounded border-gray-300 text-amber-600 focus:ring-amber-500"
            />
            <span className="text-sm text-gray-700">Available for Last Minute Offers</span>
          </label>
        </div>
      </div>

      {/* VAT Number */}
      <Input
        type="text"
        name="vatNumber"
        placeholder="Enter your VAT Number"
        value={formState.data.vatNumber}
        onChange={(e) => setValue('vatNumber', e.target.value)}
        onBlur={() => {
          setTouched('vatNumber', true);
          setTimeout(() => validateField('vatNumber'), 0);
        }}
        error={formState.errors.vatNumber}
        required
        icon={
          <span className="text-gray-400 font-medium">#</span>
        }
        label="VAT Number"
      />

      {/* Barber Certificate - Commented out as not in API schema */}
      {/*
      <FileUpload
        label="Barbers Certificate"
        placeholder="Upload your Barbers Certificate"
        accept=".pdf,.jpg,.jpeg,.png"
        onChange={handleFileChange}
        required
      />
      */}

      {/* Password */}
      <Input
        type={showPassword ? 'text' : 'password'}
        name="password"
        placeholder="• • • • • • • •"
        value={formState.data.password}
        onChange={(e) => setValue('password', e.target.value)}
        onBlur={() => {
          setTouched('password', true);
          setTimeout(() => validateField('password'), 0);
        }}
        error={formState.errors.password}
        required
        icon={
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
          </svg>
        }
        rightIcon={
          <button
            type="button"
            onClick={() => setShowPassword(!showPassword)}
            className="hover:text-gray-600"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              {showPassword ? (
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
              ) : (
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              )}
            </svg>
          </button>
        }
        label="Password"
      />

      {/* Submit Button */}
      <Button type="submit" className="w-full" size="lg">
        Register
      </Button>

      {/* Divider */}
      <div className="text-center">
        <span className="text-gray-500 text-sm">or Continue with</span>
      </div>

      {/* Google Sign Up */}
      <Button
        type="button"
        variant="outline"
        onClick={handleGoogleSignUp}
        className="w-full flex items-center justify-center gap-3"
        size="lg"
      >
        <Image
          src="/assets/images/google-logo.svg"
          alt="Google"
          width={20}
          height={20}
        />
        Sign up with Google
      </Button>

      {/* Login Link */}
      <div className="text-center">
        <span className="text-gray-600 text-sm">
          Already have an account?{' '}
          <button
            type="button"
            onClick={() => router.push('/auth/login')}
            className="text-amber-600 hover:text-amber-700 font-medium"
          >
            Login
          </button>
        </span>
      </div>
    </form>
  );
}

// Re-export auth constants from shared constants
export {
  AUTH_ROUTES,
  USER_ROLES,
  EXPERIENCE_OPTIONS,
  LANGUAGE_OPTIONS,
  STATE_OPTIONS,
  AUTH_VALIDATION_RULES,
  FILE_UPLOAD_CONFIG,
} from '@/shared/constants/auth';

// Feature-specific constants
export const SIGNUP_TABS_CONFIG = [
  {
    id: 'client' as const,
    label: 'Client',
  },
  {
    id: 'barber' as const,
    label: 'Barber',
  },
  {
    id: 'shop_owner' as const,
    label: 'Shop Owner',
  },
];

export const OTP_LENGTH = 6;
export const OTP_RESEND_TIMEOUT = 60; // seconds

export const SUBSCRIPTION_PLANS = [
  {
    id: 'annual' as const,
    name: 'Annual',
    price: '$95.99',
    period: 'Year',
    description: 'Start first 7 days free, Then $95.99 / Year',
    badge: 'Best Value',
  },
  {
    id: 'monthly' as const,
    name: 'Monthly',
    price: '$12.99',
    period: 'Month',
    description: 'Start first 7 days free, Then $12.99 / Month',
  },
];

export const USER_TYPE_LABELS = {
  client: 'Client',
  barber: 'Barber',
  'shop_owner': 'Shop Owner',
} as const;

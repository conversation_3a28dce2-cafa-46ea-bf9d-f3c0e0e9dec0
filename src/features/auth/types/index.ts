// Import types for use in interfaces
import type {
  User,
  UserRole,
  LoginFormData,
} from '@/types/auth';

// Re-export auth types from global types
export type {
  UserRole,
  PlanType,
  BaseUser,
  Client,
  Barber,
  ShopOwner,
  User,
  ClientFormData,
  BarberFormData,
  ShopOwnerFormData,
  Address,
  AuthState,
  AuthResponse,
  LoginFormData,
  ResetPasswordFormData,
  OTPVerificationData,
  EmailVerificationData,
} from '@/types/auth';

// Feature-specific types
export interface AuthFormState<T> {
  data: T;
  errors: Partial<Record<keyof T, string>>;
  isSubmitting: boolean;
  isValid: boolean;
  touched: Partial<Record<keyof T, boolean>>;
}

export interface AuthContextValue {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  login: (credentials: LoginFormData) => Promise<void>;
  register: (userData: any, role: UserRole) => Promise<void>;
  logout: () => void;
  clearError: () => void;
  refreshToken: () => Promise<void>;
}

export interface SignupTabConfig {
  id: UserRole;
  label: string;
  icon: React.ReactNode;
}

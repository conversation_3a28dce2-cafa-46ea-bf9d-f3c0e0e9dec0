'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { 
  User, 
  LoginFormData, 
  UserRole,
  ClientFormData,
  BarberFormData,
  ShopOwnerFormData, 
  AuthResponse
} from '@/types/auth';
import { authApiService } from '../services/auth-api';
import { AUTH_ROUTES } from '../constants';

interface UseAuthReturn {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  login: (credentials: LoginFormData) => Promise<void>;
  register: (userData: any, role: UserRole) => Promise<void>;
  logout: () => void;
  clearError: () => void;
  refreshToken: () => Promise<void>;
}

export function useAuth(): UseAuthReturn {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  const isAuthenticated = !!user;

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const login = useCallback(async (credentials: LoginFormData) => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await authApiService.login(credentials);

      // Check if email verification is required
      if ('requiresEmailVerification' in response && response.requiresEmailVerification) {
        setError(response.message);
        // Could redirect to email verification page here if needed
        // router.push(`${AUTH_ROUTES.VERIFY_EMAIL}?email=${credentials.email}`);
        return;
      }

      // Successful login
      const authResponse = response as AuthResponse;
      setUser(authResponse.user);

      // Redirect to dashboard
      router.push(AUTH_ROUTES.DASHBOARD);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Login failed');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [router]);

  const register = useCallback(async (
    userData: ClientFormData | BarberFormData | ShopOwnerFormData,
    role: UserRole
  ) => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await authApiService.register(userData, role);
      setUser(response.user);

      // Check if OTP was sent for email verification
      if (response.otpSent) {
        // Redirect to email verification with email and role
        router.push(`${AUTH_ROUTES.VERIFY_EMAIL}?type=${role}&email=${encodeURIComponent(userData.email)}`);
      } else {
        // If no OTP needed, redirect directly to dashboard
        router.push(AUTH_ROUTES.DASHBOARD);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Registration failed');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [router]);

  const logout = useCallback(async () => {
    try {
      setIsLoading(true);
      await authApiService.logout();
      setUser(null);
      router.push(AUTH_ROUTES.HOME);
    } catch (err) {
      console.error('Logout error:', err);
      // Clear local state even if API call fails
      setUser(null);
      router.push(AUTH_ROUTES.HOME);
    } finally {
      setIsLoading(false);
    }
  }, [router]);

  const refreshToken = useCallback(async () => {
    try {
      const authResponse = await authApiService.refreshToken();
      setUser(authResponse.user);
    } catch (err) {
      console.error('Token refresh failed:', err);
      setUser(null);
      router.push(AUTH_ROUTES.LOGIN);
    }
  }, [router]);

  // Initialize auth state on mount
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        if (authApiService.isAuthenticated()) {
          const currentUser = authApiService.getCurrentUser();
          if (currentUser) {
            setUser(currentUser);
          } else {
            // Try to refresh token if user data is missing
            await refreshToken();
          }
        }
      } catch (err) {
        console.error('Auth initialization failed:', err);
        setUser(null);
      } finally {
        setIsLoading(false);
      }
    };

    initializeAuth();
  }, [refreshToken]);

  return {
    user,
    isAuthenticated,
    isLoading,
    error,
    login,
    register,
    logout,
    clearError,
    refreshToken,
  };
}

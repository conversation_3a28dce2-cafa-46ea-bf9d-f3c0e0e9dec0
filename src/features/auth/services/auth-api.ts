// Authentication API service
import {
  LoginFormData,
  AuthResponse,
  LoginApiResponse,
  RegisterApiResponse,
  User,
  UserRole,
  ClientFormData,
  BarberFormData,
  ShopOwnerFormData,
  OTPVerificationData,
  EmailVerificationData,
  ResetPasswordFormData
} from '@/types/auth';
import { ApiResponse } from '@/types/api';
import { API_CONFIG, STORAGE_KEYS } from '@/shared/constants';

class AuthApiService {
  private baseUrl = API_CONFIG.BASE_URL;

  private async request<T>(
    endpoint: string, 
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseUrl}${endpoint}`;
    const token = this.getStoredToken();

    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...(token && { Authorization: `Bearer ${token}` }),
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Request failed');
      }

      return data;
    } catch (error) {
      console.error('API Request failed:', error);
      throw error;
    }
  }

  private getStoredToken(): string | null {
    if (typeof window === 'undefined') return null;
    return localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);
  }

  private storeTokens(authResponse: AuthResponse): void {
    if (typeof window === 'undefined') return;
    localStorage.setItem(STORAGE_KEYS.AUTH_TOKEN, authResponse.accessToken);
    localStorage.setItem(STORAGE_KEYS.REFRESH_TOKEN, authResponse.refreshToken);
    localStorage.setItem(STORAGE_KEYS.USER_DATA, JSON.stringify(authResponse.user));
  }

  private clearTokens(): void {
    if (typeof window === 'undefined') return;
    localStorage.removeItem(STORAGE_KEYS.AUTH_TOKEN);
    localStorage.removeItem(STORAGE_KEYS.REFRESH_TOKEN);
    localStorage.removeItem(STORAGE_KEYS.USER_DATA);
  }

  async login(credentials: LoginFormData): Promise<AuthResponse | { requiresEmailVerification: boolean; user: Partial<User>; message: string }> {
    const response = await fetch(`${this.baseUrl}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(credentials),
    });

    const data: LoginApiResponse = await response.json();

    if (!response.ok) {
      throw new Error(data.error || 'Login failed');
    }

    if (data.success && data.data) {
      // Check if email verification is required
      if (data.data.requiresEmailVerification) {
        return {
          requiresEmailVerification: true,
          user: data.data.user as Partial<User>,
          message: data.message || 'Please verify your email address'
        };
      }

      // Transform API response to match our AuthResponse interface
      const authResponse: AuthResponse = {
        user: {
          id: data.data.user.id,
          firstName: data.data.user.firstName,
          lastName: data.data.user.lastName,
          email: data.data.user.email,
          role: data.data.user.role,
          isEmailVerified: data.data.user.isEmailVerified,
          status: data.data.user.status,
          lastLoginAt: data.data.user.lastLoginAt,
          clientProfile: data.data.user.clientProfile,
        } as User,
        accessToken: data.data.accessToken,
        refreshToken: data.data.refreshToken,
      };

      this.storeTokens(authResponse);
      return authResponse;
    }

    throw new Error(data.error || 'Login failed');
  }

  async register(
    userData: ClientFormData | BarberFormData | ShopOwnerFormData,
    role: UserRole
  ): Promise<{ user: User; accessToken: string; refreshToken: string; message: string; otpSent: boolean }> {
    const response = await fetch(`${this.baseUrl}/auth/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ ...userData, role }),
    });

    const data: RegisterApiResponse = await response.json();

    if (!response.ok) {
      // Handle validation errors
      if (response.status === 400 && data.details) {
        const errorMessages = data.details.map(detail => `${detail.field}: ${detail.message}`).join(', ');
        throw new Error(errorMessages);
      }
      throw new Error(data.error || 'Registration failed');
    }

    if (data.success && data.data) {
      // Transform API response to match our expected format
      const result = {
        user: {
          id: data.data.user.id,
          firstName: data.data.user.firstName,
          lastName: data.data.user.lastName,
          email: data.data.user.email,
          role: data.data.user.role,
          isEmailVerified: data.data.user.isEmailVerified,
          status: data.data.user.status,
          createdAt: new Date(data.data.user.createdAt),
        } as User,
        accessToken: data.data.accessToken,
        refreshToken: data.data.refreshToken,
        message: data.data.message,
        otpSent: data.data.otpSent,
      };

      // Store tokens for authenticated state
      this.storeTokens({
        user: result.user,
        accessToken: result.accessToken,
        refreshToken: result.refreshToken,
      });

      return result;
    }

    throw new Error(data.error || 'Registration failed');
  }

  async verifyEmail(data: EmailVerificationData): Promise<void> {
    const response = await this.request<void>('/auth/verify-email', {
      method: 'POST',
      body: JSON.stringify(data),
    });

    if (!response.success) {
      throw new Error(response.error || 'Email verification failed');
    }
  }

  async sendOTP(email: string, type: 'password-reset' | 'email-verification'): Promise<void> {
    const response = await this.request<void>('/auth/send-otp', {
      method: 'POST',
      body: JSON.stringify({ email, type }),
    });

    if (!response.success) {
      throw new Error(response.error || 'Failed to send OTP');
    }
  }

  async verifyOTP(data: OTPVerificationData): Promise<void> {
    const response = await this.request<void>('/auth/verify-otp', {
      method: 'POST',
      body: JSON.stringify(data),
    });

    if (!response.success) {
      throw new Error(response.error || 'OTP verification failed');
    }
  }

  async verifyEmailOTP(email: string, otp: string): Promise<{ success: boolean; message: string }> {
    const response = await fetch(`${this.baseUrl}/auth/verify-email-otp`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email, otp }),
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.error || 'Email verification failed');
    }

    return {
      success: data.success,
      message: data.message || 'Email verified successfully'
    };
  }

  async resetPassword(data: ResetPasswordFormData & { resetToken: string }): Promise<void> {
    const response = await this.request<void>('/auth/reset-password', {
      method: 'POST',
      body: JSON.stringify(data),
    });

    if (!response.success) {
      throw new Error(response.error || 'Password reset failed');
    }
  }

  async refreshToken(): Promise<AuthResponse> {
    const refreshToken = localStorage.getItem(STORAGE_KEYS.REFRESH_TOKEN);
    if (!refreshToken) {
      throw new Error('No refresh token available');
    }

    const response = await this.request<AuthResponse>('/auth/refresh', {
      method: 'POST',
      body: JSON.stringify({ refreshToken }),
    });

    if (response.success && response.data) {
      this.storeTokens(response.data);
      return response.data;
    }

    throw new Error(response.error || 'Token refresh failed');
  }

  async logout(): Promise<void> {
    try {
      await this.request<void>('/auth/logout', {
        method: 'POST',
      });
    } catch (error) {
      console.error('Logout request failed:', error);
    } finally {
      this.clearTokens();
    }
  }

  // Forgot Password API methods
  async requestPasswordReset(email: string): Promise<{ success: boolean; message: string }> {
    const response = await fetch(`${this.baseUrl}/auth/forgot-password`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email }),
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.error || 'Failed to send password reset OTP');
    }

    return {
      success: data.success,
      message: data.message || 'Password reset OTP sent successfully'
    };
  }

  async resetPasswordWithOTP(email: string, otp: string, newPassword: string): Promise<{ success: boolean; message: string }> {
    const response = await fetch(`${this.baseUrl}/auth/reset-password-otp`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email, otp, newPassword }),
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.error || 'Password reset failed');
    }

    return {
      success: data.success,
      message: data.message || 'Password reset successful'
    };
  }

  async resendPasswordResetOTP(email: string): Promise<{ success: boolean; message: string }> {
    const response = await fetch(`${this.baseUrl}/auth/resend-password-reset-otp`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email }),
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.error || 'Failed to resend password reset OTP');
    }

    return {
      success: data.success,
      message: data.message || 'Password reset OTP sent successfully'
    };
  }

  getCurrentUser(): User | null {
    if (typeof window === 'undefined') return null;
    const userData = localStorage.getItem(STORAGE_KEYS.USER_DATA);
    return userData ? JSON.parse(userData) : null;
  }

  isAuthenticated(): boolean {
    return !!this.getStoredToken();
  }
}

export const authApiService = new AuthApiService();

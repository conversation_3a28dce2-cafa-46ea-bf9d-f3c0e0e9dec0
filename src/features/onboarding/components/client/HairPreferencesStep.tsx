'use client';

import { OptionButton, MultiSelectTags } from '@/shared/components/ui';

interface OnboardingData {
  hairLength: string[];
  hairType: string[];
  preferredStyles: string[];
  budget: string[];
  language: string[];
}

interface HairPreferencesStepProps {
  data: OnboardingData;
  onUpdate: (data: Partial<OnboardingData>) => void;
}

export function HairPreferencesStep({ data, onUpdate }: HairPreferencesStepProps) {
  const hairLengthOptions = ['Short', 'Medium', 'Long', 'Bald/Shaved'];
  const hairTypeOptions = ['Straight', 'Wavy', 'Curly'];
  const preferredStylesOptions = [
    'Undercut',
    'Skin Fade',
    'Dreadlocks',
    'Afro',
    'Taper Fade',
    'Burst Fade',
    'Buzz Cut',
    'Slick Back',
    'Classic Scissor Cut',
  ];

  const handleSelection = (category: keyof OnboardingData, value: string, allowMultiple = false) => {
    const currentSelection = data[category] || [];
    let newSelection: string[];

    if (allowMultiple) {
      if (currentSelection.includes(value)) {
        newSelection = currentSelection.filter(item => item !== value);
      } else {
        newSelection = [...currentSelection, value];
      }
    } else {
      newSelection = currentSelection.includes(value) ? [] : [value];
    }

    onUpdate({ [category]: newSelection });
  };

  const handleMultiSelectChange = (category: keyof OnboardingData, values: string[]) => {
    onUpdate({ [category]: values });
  };

  return (
    <div className="space-y-8">
      {/* Hair Length */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Hair Length</h3>
        <div className="flex flex-wrap gap-3">
          {hairLengthOptions.map((option) => (
            <OptionButton
              key={option}
              selected={data.hairLength?.includes(option)}
              onClick={() => handleSelection('hairLength', option)}
              variant="primary"
            >
              {option}
            </OptionButton>
          ))}
        </div>
      </div>

      {/* Hair Type */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Hair Type</h3>
        <div className="flex flex-wrap gap-3">
          {hairTypeOptions.map((option) => (
            <OptionButton
              key={option}
              selected={data.hairType?.includes(option)}
              onClick={() => handleSelection('hairType', option)}
              variant="primary"
            >
              {option}
            </OptionButton>
          ))}
        </div>
      </div>

      {/* Preferred Styles - Multi-select with custom option */}
      <MultiSelectTags
        label="Preferred Styles"
        options={preferredStylesOptions}
        selectedValues={data.preferredStyles || []}
        onChange={(values) => handleMultiSelectChange('preferredStyles', values)}
        allowCustom={true}
        customLabel="Other"
        variant="primary"
      />
    </div>
  );
}
'use client';

import { OptionButton, MultiSelectTags } from '@/shared/components/ui';

interface OnboardingData {
  hairLength: string[];
  hairType: string[];
  preferredStyles: string[];
  budget: string[];
  language: string[];
}

interface BudgetLanguageStepProps {
  data: OnboardingData;
  onUpdate: (data: Partial<OnboardingData>) => void;
}

export function BudgetLanguageStep({ data, onUpdate }: BudgetLanguageStepProps) {
  const budgetOptions = ['$12-$25', '$25-$35', '$36-$55'];
  const languageOptions = [
    'English',
    'Spanish',
    'Arabic',
    'Somali',
    'Urdu',
    'Turkish',
    'Kurdish',
    'German',
    'Swedish',
  ];

  const handleSelection = (category: keyof OnboardingData, value: string, allowMultiple = false) => {
    const currentSelection = data[category] || [];
    let newSelection: string[];

    if (allowMultiple) {
      if (currentSelection.includes(value)) {
        newSelection = currentSelection.filter(item => item !== value);
      } else {
        newSelection = [...currentSelection, value];
      }
    } else {
      newSelection = currentSelection.includes(value) ? [] : [value];
    }

    onUpdate({ [category]: newSelection });
  };

  const handleMultiSelectChange = (category: keyof OnboardingData, values: string[]) => {
    onUpdate({ [category]: values });
  };

  return (
    <div className="space-y-8">
      {/* Budget */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Budget</h3>
        <div className="flex flex-wrap gap-3">
          {budgetOptions.map((option) => (
            <OptionButton
              key={option}
              selected={data.budget?.includes(option)}
              onClick={() => handleSelection('budget', option)}
              variant="primary"
            >
              {option}
            </OptionButton>
          ))}
        </div>
      </div>

      {/* Language - Multi-select with custom option */}
      <MultiSelectTags
        label="Language"
        options={languageOptions}
        selectedValues={data.language || []}
        onChange={(values) => handleMultiSelectChange('language', values)}
        allowCustom={true}
        customLabel="Other"
        variant="primary"
      />
    </div>
  );
}
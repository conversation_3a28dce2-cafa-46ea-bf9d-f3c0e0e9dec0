import { OnboardingData, OnboardingStepProps } from '@/types';

// Re-export onboarding types from global types
export type {
  ClientPreferences,
  OnboardingData,
  OnboardingStepProps,
} from '@/types/onboarding';

// Feature-specific types
export interface OnboardingContextValue {
  currentStep: number;
  totalSteps: number;
  data: OnboardingData;
  isLoading: boolean;
  error: string | null;
  goToStep: (step: number) => void;
  nextStep: () => void;
  previousStep: () => void;
  updateData: (stepData: Partial<OnboardingData>) => void;
  submitOnboarding: () => Promise<void>;
  resetOnboarding: () => void;
}

export interface OnboardingStepConfig {
  id: number;
  title: string;
  description?: string;
  component: React.ComponentType<OnboardingStepProps>;
  isOptional?: boolean;
  validation?: (data: OnboardingData) => string | null;
}

export interface OnboardingFormState {
  data: OnboardingData;
  errors: Record<string, string>;
  isSubmitting: boolean;
  currentStep: number;
  completedSteps: Set<number>;
}

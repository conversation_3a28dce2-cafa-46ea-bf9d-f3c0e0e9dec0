// Onboarding API service
import { OnboardingData } from '@/types/onboarding';
import { ApiResponse } from '@/types/api';
import { API_CONFIG, STORAGE_KEYS } from '@/shared/constants';

class OnboardingApiService {
  private baseUrl = API_CONFIG.BASE_URL;

  private async request<T>(
    endpoint: string, 
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseUrl}${endpoint}`;
    const token = this.getStoredToken();

    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...(token && { Authorization: `Bearer ${token}` }),
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Request failed');
      }

      return data;
    } catch (error) {
      console.error('API Request failed:', error);
      throw error;
    }
  }

  private getStoredToken(): string | null {
    if (typeof window === 'undefined') return null;
    return localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);
  }

  async saveOnboardingData(data: OnboardingData): Promise<void> {
    const response = await this.request<void>('/onboarding/save', {
      method: 'POST',
      body: JSON.stringify(data),
    });

    if (!response.success) {
      throw new Error(response.error || 'Failed to save onboarding data');
    }
  }

  async completeOnboarding(data: OnboardingData): Promise<void> {
    const response = await this.request<void>('/onboarding/complete', {
      method: 'POST',
      body: JSON.stringify(data),
    });

    if (!response.success) {
      throw new Error(response.error || 'Failed to complete onboarding');
    }
  }

  async getOnboardingData(): Promise<OnboardingData | null> {
    try {
      const response = await this.request<OnboardingData>('/onboarding/data');
      return response.success ? response.data : null;
    } catch (error) {
      console.error('Failed to fetch onboarding data:', error);
      return null;
    }
  }

  // Local storage methods for offline support
  saveOnboardingDataLocally(data: OnboardingData): void {
    if (typeof window === 'undefined') return;
    localStorage.setItem(STORAGE_KEYS.ONBOARDING_DATA, JSON.stringify(data));
  }

  getOnboardingDataLocally(): OnboardingData | null {
    if (typeof window === 'undefined') return null;
    const data = localStorage.getItem(STORAGE_KEYS.ONBOARDING_DATA);
    return data ? JSON.parse(data) : null;
  }

  clearOnboardingDataLocally(): void {
    if (typeof window === 'undefined') return;
    localStorage.removeItem(STORAGE_KEYS.ONBOARDING_DATA);
  }
}

export const onboardingApiService = new OnboardingApiService();

'use client';

import { useState, useCallback, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { OnboardingData } from '@/types/onboarding';
import { onboardingApiService } from '../services/onboarding-api';
import { ONBOARDING_CONFIG, ONBOARDING_ROUTES } from '../constants';

interface UseOnboardingReturn {
  currentStep: number;
  totalSteps: number;
  data: OnboardingData;
  isLoading: boolean;
  error: string | null;
  goToStep: (step: number) => void;
  nextStep: () => void;
  previousStep: () => void;
  updateData: (stepData: Partial<OnboardingData>) => void;
  submitOnboarding: () => Promise<void>;
  resetOnboarding: () => void;
}

const initialOnboardingData: OnboardingData = {
  hairLength: [],
  hairType: [],
  preferredStyles: [],
  budget: [],
  language: [],
};

export function useOnboarding(): UseOnboardingReturn {
  const [currentStep, setCurrentStep] = useState(1);
  const [data, setData] = useState<OnboardingData>(initialOnboardingData);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const router = useRouter();
  const searchParams = useSearchParams();
  const totalSteps = ONBOARDING_CONFIG.TOTAL_STEPS;

  // Initialize step from URL params
  useEffect(() => {
    const stepParam = searchParams.get('step');
    if (stepParam) {
      const step = parseInt(stepParam, 10);
      if (step >= 1 && step <= totalSteps) {
        setCurrentStep(step);
      }
    }
  }, [searchParams, totalSteps]);

  // Load saved onboarding data on mount
  useEffect(() => {
    const loadOnboardingData = async () => {
      try {
        setIsLoading(true);
        
        // Try to load from API first, then fallback to local storage
        let savedData = await onboardingApiService.getOnboardingData();
        if (!savedData) {
          savedData = onboardingApiService.getOnboardingDataLocally();
        }
        
        if (savedData) {
          setData(savedData);
        }
      } catch (err) {
        console.error('Failed to load onboarding data:', err);
        setError('Failed to load your progress');
      } finally {
        setIsLoading(false);
      }
    };

    loadOnboardingData();
  }, []);

  const goToStep = useCallback((step: number) => {
    if (step >= 1 && step <= totalSteps) {
      setCurrentStep(step);
      router.push(`/auth/onboarding/client?step=${step}`);
    }
  }, [totalSteps, router]);

  const nextStep = useCallback(() => {
    if (currentStep < totalSteps) {
      goToStep(currentStep + 1);
    } else {
      // Complete onboarding
      submitOnboarding();
    }
  }, [currentStep, totalSteps, goToStep]);

  const previousStep = useCallback(() => {
    if (currentStep > 1) {
      goToStep(currentStep - 1);
    }
  }, [currentStep, goToStep]);

  const updateData = useCallback((stepData: Partial<OnboardingData>) => {
    setData(prev => {
      const newData = { ...prev, ...stepData };
      
      // Save to local storage for offline support
      onboardingApiService.saveOnboardingDataLocally(newData);
      
      return newData;
    });
    
    setError(null);
  }, []);

  const submitOnboarding = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      await onboardingApiService.completeOnboarding(data);
      
      // Clear local storage after successful submission
      onboardingApiService.clearOnboardingDataLocally();
      
      // Redirect to welcome page
      router.push(ONBOARDING_ROUTES.COMPLETE);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to complete onboarding');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [data, router]);

  const resetOnboarding = useCallback(() => {
    setData(initialOnboardingData);
    setCurrentStep(1);
    setError(null);
    onboardingApiService.clearOnboardingDataLocally();
    router.push('/auth/onboarding/client?step=1');
  }, [router]);

  // Auto-save data periodically
  useEffect(() => {
    const saveData = async () => {
      try {
        await onboardingApiService.saveOnboardingData(data);
      } catch (err) {
        console.error('Failed to auto-save onboarding data:', err);
        // Don't show error to user for auto-save failures
      }
    };

    // Only auto-save if we have some data
    if (data.preferences && Object.values(data.preferences).some(arr => arr.length > 0)) {
      const timeoutId = setTimeout(saveData, 2000); // Save after 2 seconds of inactivity
      return () => clearTimeout(timeoutId);
    }
  }, [data]);

  return {
    currentStep,
    totalSteps,
    data,
    isLoading,
    error,
    goToStep,
    nextStep,
    previousStep,
    updateData,
    submitOnboarding,
    resetOnboarding,
  };
}

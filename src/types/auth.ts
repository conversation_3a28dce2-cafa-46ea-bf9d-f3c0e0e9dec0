import { ClientPreferences } from "./onboarding";

// Authentication related types
export type UserRole = 'client' | 'barber' | 'shop_owner';
export type PlanType = 'annual' | 'monthly';

// Base user interface
export interface BaseUser {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: UserRole;
  isEmailVerified: boolean;
  status: string;
  lastLoginAt?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

// Client specific data
export interface ClientFormData {
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  password: string;
  role: 'client';
  vatNumber: string;
  experience_years: string; // enum: "0-1", "1-3", "3-5", "5-10", "10-15", "15-20", "20+"
}

export interface Client extends BaseUser {
  role: 'client';
  location?: string;
  preferences?: ClientPreferences;
  clientProfile?: {
    userId: string;
    locationId: string;
  };
}

// Barber specific data
export interface Address {
  street: string;
  zipcode: string;
  city: string;
  state: string;
}

export interface BarberFormData {
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  password: string;
  // Optional fields for all roles
  profilePhoto?: string;
  // Barber-specific fields (when role is "barber")
  bio?: string; // Optional in backend
  experienceYears: string; // Maps to experience field: "0-1", "1-3", "3-5", "5-10", "10-15", "15-20", "20+"
  specialties?: string[]; // Optional: ["haircut", "beard", "styling"]
  vatNumber: string; // 8-50 characters
  workingType?: string; // Optional: "solo" or "in_shop"
  portfolioUrl?: string; // Optional
  isKidFriendly?: boolean; // Optional
  isAvailableForLastMinuteOffer?: boolean; // Optional
  // Frontend-only fields (not in API schema)
  // language: string; // Commented out - not in API schema
  // address: Address; // Commented out - not in API schema
  // barberCertificate: File | null; // Commented out - not in API schema
}

export interface Barber extends BaseUser {
  role: 'barber';
  phoneNumber: string;
  experience: string;
  language: string;
  address: Address;
  vatNumber: string;
  barberCertificate?: string; // URL to uploaded certificate
  isVerified: boolean;
}

// Shop Owner specific data
export interface ShopOwnerFormData {
  firstName: string; // Maps to shopOwnerName
  lastName: string;
  email: string; // Maps to shopEmail
  phoneNumber: string;
  password: string;
  // Optional fields for all roles
  profilePhoto?: string;
  // Shop Owner specific fields
  bio?: string; // Optional
  experienceYears: string; // Maps to experience field
  vatNumber: string; // 8-50 characters
  businessName: string; // Required by API
  // Frontend-only fields (not in API schema)
  // language: string; // Commented out - not in API schema
  // address: Address; // Commented out - not in API schema
  // barberCertificate: File | null; // Commented out - not in API schema
}

export interface ShopOwner extends BaseUser {
  role: 'shop_owner';
  shopName: string;
  shopOwnerName: string;
  shopEmail: string;
  phoneNumber: string;
  experience: string;
  language: string;
  address: Address;
  vatNumber: string;
  barberCertificate?: string; // URL to uploaded certificate
  isVerified: boolean;
}

// Union type for all users
export type User = Client | Barber | ShopOwner;

// Authentication state
export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

// Login/Register responses
export interface AuthResponse {
  user: User;
  accessToken: string;
  refreshToken: string;
}

// API Response format for login
export interface LoginApiResponse {
  success: boolean;
  data?: {
    user: {
      id: string;
      firstName: string;
      lastName: string;
      email: string;
      role: UserRole;
      isEmailVerified: boolean;
      status: string;
      lastLoginAt: string;
      clientProfile?: {
        userId: string;
        locationId: string;
      };
      barberProfile?: Record<string, unknown>;
      shopOwnerProfile?: Record<string, unknown>;
    };
    accessToken: string;
    refreshToken: string;
    requiresEmailVerification?: boolean;
  };
  message?: string;
  error?: string;
}

// API Response format for registration
export interface RegisterApiResponse {
  success: boolean;
  data?: {
    user: {
      id: string;
      firstName: string;
      lastName: string;
      email: string;
      role: UserRole;
      isEmailVerified: boolean;
      status: string;
      createdAt: string;
    };
    accessToken: string;
    refreshToken: string;
    message: string;
    otpSent: boolean;
  };
  error?: string;
  details?: Array<{
    field: string;
    message: string;
    value: any;
  }>;
}

// Login form data
export interface LoginFormData {
  email: string;
  password: string;
}

// Password reset data
export interface ResetPasswordFormData {
  password: string;
  confirmPassword: string;
}

// OTP verification
export interface OTPVerificationData {
  email: string;
  otp: string;
}

// Email verification
export interface EmailVerificationData {
  email: string;
  verificationCode: string;
}

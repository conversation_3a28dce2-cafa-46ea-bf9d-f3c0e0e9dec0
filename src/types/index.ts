// Central export for all types
export * from './auth';
export * from './onboarding';
export * from './ui';
export * from './api';

// Common utility types
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

// Form state types
export interface FormState<T> {
  data: T;
  errors: Partial<Record<keyof T, string>>;
  isSubmitting: boolean;
  isValid: boolean;
}

// Generic callback types
export type VoidCallback = () => void;
export type ValueCallback<T> = (value: T) => void;
export type EventCallback<T = Event> = (event: T) => void;

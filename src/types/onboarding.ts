// Onboarding related types

// Client preferences during onboarding
export interface ClientPreferences {
  hairLength: string[];
  hairType: string[];
  preferredStyles: string[];
  budget: string[];
  language: string[];
}

// Onboarding step data structure
export type OnboardingData = ClientPreferences;

// Individual step props
export interface OnboardingStepProps {
  data: OnboardingData;
  onUpdate: (data: Partial<OnboardingData>) => void;
}

// Onboarding flow state
export interface OnboardingState {
  currentStep: number;
  totalSteps: number;
  data: OnboardingData;
  isComplete: boolean;
}

// Step configuration
export interface OnboardingStep {
  id: number;
  title: string;
  component: React.ComponentType<OnboardingStepProps>;
  isRequired: boolean;
}

// Onboarding completion data
export interface OnboardingCompletionData {
  userId: string;
  preferences: ClientPreferences;
  completedAt: Date;
}

'use client';

import { useState, useCallback, useMemo } from 'react';
import { AUTH_VALIDATION_RULES } from '@/shared/constants/auth';
import { VALIDATION_PATTERNS, VALIDATION_LENGTHS } from '@/shared/constants/validation';

export type ValidationRule<T> = (value: T) => string | undefined;
export type ValidationRules<T> = {
  [K in keyof T]?: ValidationRule<T[K]>;
};

export interface FormState<T> {
  data: T;
  errors: Partial<Record<keyof T, string>>;
  touched: Partial<Record<keyof T, boolean>>;
  isSubmitting: boolean;
  isValid: boolean;
}

interface UseFormValidationOptions<T> {
  initialData: T;
  validationRules?: ValidationRules<T>;
  validateOnChange?: boolean;
  validateOnBlur?: boolean;
  onSubmit?: (data: T) => Promise<void> | void;
}

interface UseFormValidationReturn<T> {
  formState: FormState<T>;
  setValue: (field: keyof T, value: T[keyof T]) => void;
  setValues: (values: Partial<T>) => void;
  setError: (field: keyof T, error: string) => void;
  clearError: (field: keyof T) => void;
  clearAllErrors: () => void;
  setTouched: (field: keyof T, touched?: boolean) => void;
  setSubmitting: (isSubmitting: boolean) => void;
  validateField: (field: keyof T) => boolean;
  validateForm: () => boolean;
  resetForm: (newData?: T) => void;
  handleSubmit: (e?: React.FormEvent) => Promise<void>;
  getFieldProps: (field: keyof T) => {
    value: T[keyof T];
    onChange: (value: T[keyof T]) => void;
    onBlur: () => void;
    error: string | undefined;
  };
}

export function useFormValidation<T extends Record<string, any>>({
  initialData,
  validationRules = {},
  validateOnChange = false,
  validateOnBlur = true,
  onSubmit,
}: UseFormValidationOptions<T>): UseFormValidationReturn<T> {
  const [formState, setFormState] = useState<FormState<T>>({
    data: initialData,
    errors: {},
    touched: {},
    isSubmitting: false,
    isValid: false,
  });

  const setValue = useCallback((field: keyof T, value: T[keyof T]) => {
    setFormState(prev => {
      const newData = { ...prev.data, [field]: value };
      const newState = {
        ...prev,
        data: newData,
        touched: { ...prev.touched, [field]: true },
      };

      // Validate on change if enabled
      if (validateOnChange && validationRules[field]) {
        const error = validationRules[field]!(value);
        newState.errors = { ...prev.errors, [field]: error };
      }

      return newState;
    });
  }, [validateOnChange, validationRules]);

  const setValues = useCallback((values: Partial<T>) => {
    setFormState(prev => ({
      ...prev,
      data: { ...prev.data, ...values },
      touched: {
        ...prev.touched,
        ...Object.keys(values).reduce((acc, key) => ({ ...acc, [key]: true }), {}),
      },
    }));
  }, []);

  const setError = useCallback((field: keyof T, error: string) => {
    setFormState(prev => ({
      ...prev,
      errors: { ...prev.errors, [field]: error },
    }));
  }, []);

  const clearError = useCallback((field: keyof T) => {
    setFormState(prev => ({
      ...prev,
      errors: { ...prev.errors, [field]: undefined },
    }));
  }, []);

  const clearAllErrors = useCallback(() => {
    setFormState(prev => ({
      ...prev,
      errors: {},
    }));
  }, []);

  const setTouched = useCallback((field: keyof T, touched = true) => {
    setFormState(prev => ({
      ...prev,
      touched: { ...prev.touched, [field]: touched },
    }));
  }, []);

  const setSubmitting = useCallback((isSubmitting: boolean) => {
    setFormState(prev => ({
      ...prev,
      isSubmitting,
    }));
  }, []);

  const validateField = useCallback((field: keyof T): boolean => {
    const rule = validationRules[field];
    if (!rule) return true;

    const error = rule(formState.data[field]);
    setError(field, error || '');
    return !error;
  }, [formState.data, validationRules, setError]);

  const validateForm = useCallback((): boolean => {
    const newErrors: Partial<Record<keyof T, string>> = {};
    let isValid = true;

    Object.keys(validationRules).forEach(field => {
      const rule = validationRules[field as keyof T];
      if (rule) {
        const error = rule(formState.data[field as keyof T]);
        if (error) {
          newErrors[field as keyof T] = error;
          isValid = false;
        }
      }
    });

    setFormState(prev => ({
      ...prev,
      errors: newErrors,
    }));

    return isValid;
  }, [formState.data, validationRules]);

  const resetForm = useCallback((newData?: T) => {
    setFormState({
      data: newData || initialData,
      errors: {},
      touched: {},
      isSubmitting: false,
      isValid: false,
    });
  }, [initialData]);

  const handleSubmit = useCallback(async (e?: React.FormEvent) => {
    if (e) {
      e.preventDefault();
    }

    setSubmitting(true);

    try {
      const isFormValid = validateForm();
      if (isFormValid && onSubmit) {
        await onSubmit(formState.data);
      }
    } catch (error) {
      console.error('Form submission error:', error);
    } finally {
      setSubmitting(false);
    }
  }, [formState.data, onSubmit, setSubmitting, validateForm]);

  const getFieldProps = useCallback((field: keyof T) => ({
    value: formState.data[field],
    onChange: (value: T[keyof T]) => setValue(field, value),
    onBlur: () => {
      setTouched(field, true);
      if (validateOnBlur) {
        validateField(field);
      }
    },
    error: formState.errors[field],
  }), [formState.data, formState.errors, setValue, setTouched, validateOnBlur, validateField]);

  // Calculate isValid based on current errors
  const isValid = useMemo(() => {
    return Object.keys(formState.errors).every(key => 
      !formState.errors[key as keyof T]
    );
  }, [formState.errors]);

  // Update formState with computed isValid
  const updatedFormState = useMemo(() => ({
    ...formState,
    isValid,
  }), [formState, isValid]);

  return {
    formState: updatedFormState,
    setValue,
    setValues,
    setError,
    clearError,
    clearAllErrors,
    setTouched,
    setSubmitting,
    validateField,
    validateForm,
    resetForm,
    handleSubmit,
    getFieldProps,
  };
}

/**
 * Common validation rules using centralized constants and error messages
 * These functions provide consistent validation across the application
 */

/**
 * Creates email validation function using centralized patterns and messages
 */
export const createEmailValidation = () => (value: string) => {
  if (!value) return AUTH_VALIDATION_RULES.EMAIL.REQUIRED;
  if (!VALIDATION_PATTERNS.EMAIL.test(value)) return AUTH_VALIDATION_RULES.EMAIL.INVALID;
  return undefined;
};

/**
 * Creates password validation function using centralized patterns and messages
 */
export const createPasswordValidation = () => (value: string) => {
  if (!value) return AUTH_VALIDATION_RULES.PASSWORD.REQUIRED;
  if (value.length < VALIDATION_LENGTHS.PASSWORD_MIN) return AUTH_VALIDATION_RULES.PASSWORD.MIN_LENGTH;
  if (!VALIDATION_PATTERNS.PASSWORD.test(value)) return AUTH_VALIDATION_RULES.PASSWORD.PATTERN;
  return undefined;
};

/**
 * Creates required field validation function
 * @param fieldName - Name of the field for error message
 */
export const createRequiredValidation = (fieldName: string) => (value: any) => {
  if (!value || (typeof value === 'string' && !value.trim())) {
    return `${fieldName} is required`;
  }
  return undefined;
};

/**
 * Creates minimum length validation function
 * @param minLength - Minimum required length
 * @param fieldName - Name of the field for error message
 */
export const createMinLengthValidation = (minLength: number, fieldName: string) => (value: string) => {
  if (value && value.length < minLength) {
    return `${fieldName} must be at least ${minLength} characters long`;
  }
  return undefined;
};

/**
 * Creates phone number validation function using centralized patterns and messages
 */
export const createPhoneValidation = () => (value: string) => {
  if (!value) return AUTH_VALIDATION_RULES.PHONE_NUMBER.REQUIRED;
  if (!VALIDATION_PATTERNS.PHONE.test(value.replace(/\s/g, ''))) return AUTH_VALIDATION_RULES.PHONE_NUMBER.INVALID;
  return undefined;
};

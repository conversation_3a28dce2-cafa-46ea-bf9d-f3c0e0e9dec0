// Central export for all constants
export * from './auth';
export * from './onboarding';
export * from './ui';

// Application wide constants
export const APP_CONFIG = {
  NAME: 'CutConnect',
  VERSION: '1.0.0',
  DESCRIPTION: 'Barber Booking System',
  SUPPORT_EMAIL: '<EMAIL>',
} as const;

export const API_CONFIG = {
  BASE_URL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api/v1',
  TIMEOUT: 30000, // 30 seconds
  RETRY_ATTEMPTS: 3,
} as const;

export const STORAGE_KEYS = {
  AUTH_TOKEN: 'auth_token',
  REFRESH_TOKEN: 'refresh_token',
  USER_DATA: 'user_data',
  ONBOARDING_DATA: 'onboarding_data',
  THEME: 'theme',
  LANGUAGE: 'language',
} as const;

export const ERROR_MESSAGES = {
  GENERIC: 'Something went wrong. Please try again.',
  NETWORK: 'Network error. Please check your connection.',
  UNAUTHORIZED: 'You are not authorized to perform this action.',
  FORBIDDEN: 'Access denied.',
  NOT_FOUND: 'The requested resource was not found.',
  SERVER_ERROR: 'Server error. Please try again later.',
  VALIDATION_ERROR: 'Please check your input and try again.',
} as const;

export const SUCCESS_MESSAGES = {
  REGISTRATION: 'Account created successfully!',
  LOGIN: 'Welcome back!',
  EMAIL_VERIFIED: 'Email verified successfully!',
  PASSWORD_RESET: 'Password reset successfully!',
  PROFILE_UPDATED: 'Profile updated successfully!',
  ONBOARDING_COMPLETE: 'Onboarding completed successfully!',
} as const;

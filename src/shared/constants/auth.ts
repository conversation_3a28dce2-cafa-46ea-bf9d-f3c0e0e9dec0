/**
 * Authentication related constants
 */
import { FILE_SIZE_LIMITS, FILE_VALIDATION } from './validation';

export const AUTH_ROUTES = {
  HOME: '/',
  LOGIN: '/auth/login',
  SIGNUP: '/auth/signup',
  FORGOT_PASSWORD: '/auth/forgot-password',
  OTP: '/auth/otp',
  RESET_PASSWORD: '/auth/reset',
  VERIFY_EMAIL: '/auth/verify-email',
  SELECT_PLAN: '/auth/select-plan',
  SUCCESS: '/auth/success',
  WELCOME: '/auth/welcome',
  ONBOARDING_CLIENT: '/auth/onboarding/client',
  DASHBOARD: '/dashboard',
} as const;

export const USER_ROLES = {
  CLIENT: 'client',
  BARBER: 'barber',
  SHOP_OWNER: 'shop_owner',
} as const;

export const EXPERIENCE_OPTIONS = [
  { value: '0-1', label: '0-1 years' },
  { value: '1-3', label: '1-3 years' },
  { value: '3-5', label: '3-5 years' },
  { value: '5-10', label: '5-10 years' },
  { value: '10-15', label: '10-15 years' },
  { value: '15-20', label: '15-20 years' },
  { value: '20+', label: '20+ years' },
];

export const LANGUAGE_OPTIONS = [
  { value: 'english', label: 'English' },
  { value: 'spanish', label: 'Spanish' },
  { value: 'arabic', label: 'Arabic' },
  { value: 'somali', label: 'Somali' },
  { value: 'urdu', label: 'Urdu' },
  { value: 'turkish', label: 'Turkish' },
  { value: 'kurdish', label: 'Kurdish' },
  { value: 'german', label: 'German' },
  { value: 'swedish', label: 'Swedish' },
];

export const STATE_OPTIONS = [
  { value: 'AL', label: 'Alabama' },
  { value: 'AK', label: 'Alaska' },
  { value: 'AZ', label: 'Arizona' },
  { value: 'AR', label: 'Arkansas' },
  { value: 'CA', label: 'California' },
  { value: 'CO', label: 'Colorado' },
  { value: 'CT', label: 'Connecticut' },
  { value: 'DE', label: 'Delaware' },
  { value: 'FL', label: 'Florida' },
  { value: 'GA', label: 'Georgia' },
  // Add more states as needed
];

export const AUTH_VALIDATION_RULES = {
  EMAIL: {
    REQUIRED: 'Email is required',
    INVALID: 'Please enter a valid email address',
  },
  PASSWORD: {
    REQUIRED: 'Password is required',
    MIN_LENGTH: 'Password must be at least 8 characters long',
    PATTERN: 'Password must contain at least one uppercase letter, one lowercase letter, and one number',
  },
  FIRST_NAME: {
    REQUIRED: 'First name is required',
    MIN_LENGTH: 'First name must be at least 2 characters long',
  },
  LAST_NAME: {
    REQUIRED: 'Last name is required',
    MIN_LENGTH: 'Last name must be at least 2 characters long',
  },
  PHONE_NUMBER: {
    REQUIRED: 'Phone number is required',
    INVALID: 'Please enter a valid phone number',
  },
  VAT_NUMBER: {
    REQUIRED: 'VAT number is required',
    INVALID: 'Please enter a valid VAT number',
  },
  ADDRESS: {
    STREET_REQUIRED: 'Street address is required',
    CITY_REQUIRED: 'City is required',
    STATE_REQUIRED: 'State is required',
    ZIPCODE_REQUIRED: 'ZIP code is required',
    ZIPCODE_INVALID: 'Please enter a valid ZIP code',
  },
  FILE: {
    REQUIRED: 'File is required',
    INVALID_TYPE: 'Invalid file type',
    TOO_LARGE: 'File size is too large',
  },
} as const;

/**
 * File upload configuration using centralized constants
 * Eliminates magic numbers and ensures consistency
 */
export const FILE_UPLOAD_CONFIG = {
  BARBER_CERTIFICATE: {
    ACCEPT: '.pdf,.jpg,.jpeg,.png',
    MAX_SIZE: FILE_SIZE_LIMITS.BARBER_CERTIFICATE_MAX,
    ALLOWED_TYPES: FILE_VALIDATION.ALLOWED_CERTIFICATE_TYPES,
  },
  UI: {
    DRAG_ACTIVE_CLASS: 'border-amber-400 bg-amber-50',
    DRAG_INACTIVE_CLASS: 'border-gray-300',
    ERROR_CLASS: 'border-red-300',
  },
} as const;

// Base API client service
import { ApiResponse, ApiError } from '@/types/api';
import { API_CONFIG, STORAGE_KEYS, ERROR_MESSAGES } from '@/shared/constants';

export interface RequestConfig extends RequestInit {
  timeout?: number;
  retries?: number;
  retryDelay?: number;
}

class ApiClient {
  private baseUrl: string;
  private defaultTimeout: number;
  private defaultRetries: number;

  constructor(baseUrl?: string) {
    this.baseUrl = baseUrl || API_CONFIG.BASE_URL;
    this.defaultTimeout = API_CONFIG.TIMEOUT;
    this.defaultRetries = API_CONFIG.RETRY_ATTEMPTS;
  }

  private getAuthToken(): string | null {
    if (typeof window === 'undefined') return null;
    return localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);
  }

  private async handleResponse<T>(response: Response): Promise<ApiResponse<T>> {
    const contentType = response.headers.get('content-type');
    const isJson = contentType?.includes('application/json');

    let data: any;
    try {
      data = isJson ? await response.json() : await response.text();
    } catch (error) {
      throw new Error('Failed to parse response');
    }

    if (!response.ok) {
      const apiError: ApiError = {
        message: data.message || ERROR_MESSAGES.GENERIC,
        status: response.status,
        code: data.code,
        details: data.details,
      };

      // Handle specific error cases
      switch (response.status) {
        case 401:
          apiError.message = ERROR_MESSAGES.UNAUTHORIZED;
          // Trigger token refresh or logout
          this.handleUnauthorized();
          break;
        case 403:
          apiError.message = ERROR_MESSAGES.FORBIDDEN;
          break;
        case 404:
          apiError.message = ERROR_MESSAGES.NOT_FOUND;
          break;
        case 422:
          apiError.message = ERROR_MESSAGES.VALIDATION_ERROR;
          break;
        case 500:
          apiError.message = ERROR_MESSAGES.SERVER_ERROR;
          break;
        default:
          if (!apiError.message) {
            apiError.message = ERROR_MESSAGES.GENERIC;
          }
      }

      return {
        success: false,
        data: null,
        error: apiError.message,
        details: apiError,
      };
    }

    return {
      success: true,
      data: data,
      error: null,
    };
  }

  private handleUnauthorized(): void {
    // Clear stored tokens
    if (typeof window !== 'undefined') {
      localStorage.removeItem(STORAGE_KEYS.AUTH_TOKEN);
      localStorage.removeItem(STORAGE_KEYS.REFRESH_TOKEN);
      localStorage.removeItem(STORAGE_KEYS.USER_DATA);
    }

    // Redirect to login (this could be handled by a global auth context)
    if (typeof window !== 'undefined' && window.location.pathname !== '/auth/login') {
      window.location.href = '/auth/login';
    }
  }

  private async requestWithTimeout(
    url: string,
    config: RequestConfig
  ): Promise<Response> {
    const { timeout = this.defaultTimeout, ...requestConfig } = config;

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    try {
      const response = await fetch(url, {
        ...requestConfig,
        signal: controller.signal,
      });
      clearTimeout(timeoutId);
      return response;
    } catch (error) {
      clearTimeout(timeoutId);
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('Request timeout');
      }
      throw error;
    }
  }

  private async requestWithRetry<T>(
    url: string,
    config: RequestConfig,
    attempt: number = 0
  ): Promise<ApiResponse<T>> {
    const { retries = this.defaultRetries, retryDelay = 1000, ...requestConfig } = config;

    try {
      const response = await this.requestWithTimeout(url, requestConfig);
      return this.handleResponse<T>(response);
    } catch (error) {
      const isLastAttempt = attempt >= retries;
      const isRetryableError = error instanceof Error && 
        (error.message.includes('fetch') || error.message.includes('network'));

      if (!isLastAttempt && isRetryableError) {
        // Exponential backoff
        const delay = retryDelay * Math.pow(2, attempt);
        await new Promise(resolve => setTimeout(resolve, delay));
        return this.requestWithRetry<T>(url, config, attempt + 1);
      }

      return {
        success: false,
        data: null,
        error: error instanceof Error ? error.message : ERROR_MESSAGES.NETWORK,
      };
    }
  }

  async request<T = any>(
    endpoint: string,
    config: RequestConfig = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseUrl}${endpoint}`;
    const token = this.getAuthToken();

    const defaultHeaders: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    if (token) {
      defaultHeaders.Authorization = `Bearer ${token}`;
    }

    const requestConfig: RequestConfig = {
      ...config,
      headers: {
        ...defaultHeaders,
        ...config.headers,
      },
    };

    return this.requestWithRetry<T>(url, requestConfig);
  }

  async get<T = any>(endpoint: string, config?: RequestConfig): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...config, method: 'GET' });
  }

  async post<T = any>(
    endpoint: string,
    data?: any,
    config?: RequestConfig
  ): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      ...config,
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async put<T = any>(
    endpoint: string,
    data?: any,
    config?: RequestConfig
  ): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      ...config,
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async patch<T = any>(
    endpoint: string,
    data?: any,
    config?: RequestConfig
  ): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      ...config,
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async delete<T = any>(endpoint: string, config?: RequestConfig): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...config, method: 'DELETE' });
  }

  // File upload method
  async uploadFile<T = any>(
    endpoint: string,
    file: File,
    additionalData?: Record<string, any>,
    config?: Omit<RequestConfig, 'body' | 'headers'>
  ): Promise<ApiResponse<T>> {
    const formData = new FormData();
    formData.append('file', file);

    if (additionalData) {
      Object.entries(additionalData).forEach(([key, value]) => {
        formData.append(key, value);
      });
    }

    const token = this.getAuthToken();
    const headers: Record<string, string> = {};
    
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }

    return this.request<T>(endpoint, {
      ...config,
      method: 'POST',
      body: formData,
      headers,
    });
  }
}

// Create and export a singleton instance
export const apiClient = new ApiClient();

// Export the class for testing or custom instances
export { ApiClient };

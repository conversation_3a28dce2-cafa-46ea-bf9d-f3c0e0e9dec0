// Simple test to verify login API integration
// This is a temporary test file to verify the integration works

const testLoginIntegration = async () => {
  const API_BASE_URL = 'http://localhost:3001/api/v1';
  
  // Test credentials
  const credentials = {
    email: '<EMAIL>',
    password: 'SecurePass123'
  };

  try {
    console.log('Testing login API integration...');
    console.log('API Base URL:', API_BASE_URL);
    console.log('Credentials:', credentials);

    const response = await fetch(`${API_BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(credentials),
    });

    console.log('Response status:', response.status);
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));

    const data = await response.json();
    console.log('Response data:', JSON.stringify(data, null, 2));

    if (response.ok && data.success) {
      console.log('✅ Login API integration successful!');
      console.log('User:', data.data.user);
      console.log('Access Token:', data.data.accessToken ? 'Present' : 'Missing');
      console.log('Refresh Token:', data.data.refreshToken ? 'Present' : 'Missing');
    } else {
      console.log('❌ Login failed:', data.error || data.message);
    }

  } catch (error) {
    console.error('❌ Network error:', error.message);
    console.log('Make sure the backend server is running on http://localhost:3001');
  }
};

// Run the test if this file is executed directly
if (typeof window === 'undefined') {
  testLoginIntegration();
}

module.exports = { testLoginIntegration };
